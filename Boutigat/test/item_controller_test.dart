import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/categories.dart';

void main() {
  group('ItemController Category Selection Tests', () {
    late ItemController itemController;

    setUp(() {
      // Initialize GetX
      Get.testMode = true;
      itemController = ItemController();
    });

    tearDown(() {
      Get.reset();
    });

    test('updateSelectedCategory should set category details correctly', () {
      // Create mock categories with details
      final mockCategoryDetails = [
        CategoryDetails(
          id: 1,
          labelEn: 'Size',
          labelAr: 'الحجم',
          labelFr: 'Taille',
          categoryId: 1,
          createdDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        CategoryDetails(
          id: 2,
          labelEn: 'Color',
          labelAr: 'اللون',
          labelFr: 'Couleur',
          categoryId: 1,
          createdDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      final mockCategory = Category(
        id: 1,
        titleEn: 'Clothing',
        titleAr: 'ملابس',
        titleFr: 'Vêtements',
        children: [],
        details: mockCategoryDetails,
      );

      // Add mock category to controller
      itemController.categories.add(mockCategory);

      // Test category selection
      itemController.updateSelectedCategory('1');

      // Verify results
      expect(itemController.categoryID.value, equals('1'));
      expect(itemController.selectedCategoryDetails.length, equals(2));
      expect(itemController.categoryDetailId.value, equals('1'));
      expect(itemController.selectedCategoryDetails[0].labelEn, equals('Size'));
      expect(itemController.selectedCategoryDetails[1].labelEn, equals('Color'));
    });

    test('updateSelectedCategory should clear details when category has no details', () {
      // Create mock category without details
      final mockCategory = Category(
        id: 2,
        titleEn: 'Electronics',
        titleAr: 'إلكترونيات',
        titleFr: 'Électronique',
        children: [],
        details: [], // No details
      );

      // Add mock category to controller
      itemController.categories.add(mockCategory);

      // Test category selection
      itemController.updateSelectedCategory('2');

      // Verify results
      expect(itemController.categoryID.value, equals('2'));
      expect(itemController.selectedCategoryDetails.length, equals(0));
      expect(itemController.categoryDetailId.value, equals(''));
    });

    test('updateSelectedCategory should use parent category details when child has none', () {
      // Create parent category with details
      final parentCategoryDetails = [
        CategoryDetails(
          id: 3,
          labelEn: 'Brand',
          labelAr: 'العلامة التجارية',
          labelFr: 'Marque',
          categoryId: 1,
          createdDate: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      final parentCategory = Category(
        id: 1,
        titleEn: 'Parent Category',
        titleAr: 'الفئة الأصل',
        titleFr: 'Catégorie Parent',
        children: [],
        details: parentCategoryDetails,
      );

      final childCategory = Category(
        id: 3,
        titleEn: 'Child Category',
        titleAr: 'الفئة الفرعية',
        titleFr: 'Catégorie Enfant',
        parentId: 1,
        children: [],
        details: [], // No details
      );

      // Add categories to controller
      itemController.categories.addAll([parentCategory, childCategory]);

      // Test child category selection
      itemController.updateSelectedCategory('3');

      // Verify results - should use parent category details
      expect(itemController.categoryID.value, equals('3'));
      expect(itemController.selectedCategoryDetails.length, equals(1));
      expect(itemController.categoryDetailId.value, equals('1')); // Parent category ID
      expect(itemController.selectedCategoryDetails[0].labelEn, equals('Brand'));
    });

    test('updateSelectedCategory should clear brand when category changes', () {
      // Set initial brand
      itemController.brand.value = 'Nike';

      // Create mock category
      final mockCategory = Category(
        id: 1,
        titleEn: 'Clothing',
        titleAr: 'ملابس',
        titleFr: 'Vêtements',
        children: [],
        details: [],
      );

      itemController.categories.add(mockCategory);

      // Test category selection
      itemController.updateSelectedCategory('1');

      // Verify brand is cleared
      expect(itemController.brand.value, equals(''));
      expect(itemController.brands.length, equals(0));
    });

    test('selectedCategoryTitle should return correct title', () {
      // Test empty category
      expect(itemController.selectedCategoryTitle, contains('select_category'));

      // Add mock category
      final mockCategory = Category(
        id: 1,
        titleEn: 'Clothing',
        titleAr: 'ملابس',
        titleFr: 'Vêtements',
        children: [],
        details: [],
      );

      itemController.categories.add(mockCategory);
      itemController.categoryID.value = '1';

      // Test with selected category
      expect(itemController.selectedCategoryTitle, equals('Clothing'));
    });
  });
}
