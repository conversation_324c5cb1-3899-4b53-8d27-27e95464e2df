import 'dart:developer';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/brands_service.dart';
import 'package:get/get.dart';

/// Service to handle category and brand selection logic
/// Separates business logic from controller concerns
class CategorySelectionService {
  static List<Category> _categories = [];
  static List<Brands> _brands = [];
  static bool _categoriesLoaded = false;

  /// Get cached categories or fetch if not loaded
  static Future<List<Category>> getCategories() async {
    if (!_categoriesLoaded || _categories.isEmpty) {
      await _loadCategories();
    }
    return _categories;
  }

  /// Load categories from API
  static Future<void> _loadCategories() async {
    try {
      var fetchedCategories = await CategoryService.fetchCategories();
      if (fetchedCategories != null) {
        _categories = fetchedCategories;
        _categoriesLoaded = true;
      }
    } catch (e) {
      log('Error loading categories: $e');
      throw Exception('Failed to load categories: $e');
    }
  }

  /// Get brands for a specific category
  static Future<List<Brands>> getBrandsForCategory(String categoryId) async {
    if (categoryId.isEmpty) {
      return [];
    }

    try {
      var fetchedBrands = await BrandService.fetchBrands(categoryId);
      if (fetchedBrands != null) {
        _brands = fetchedBrands;
        return _brands;
      }
      return [];
    } catch (e) {
      log('Error fetching brands for category $categoryId: $e');
      return [];
    }
  }

  /// Find category by ID with recursive search
  static Category? findCategoryById(String id) {
    if (id.isEmpty || _categories.isEmpty) {
      return null;
    }

    for (var category in _categories) {
      if (category.id.toString() == id) {
        return category;
      }
      
      // Search in children recursively
      var found = _findCategoryByIdRecursive(category, id);
      if (found != null) {
        return found;
      }
    }
    return null;
  }

  /// Recursive helper to find category by ID
  static Category? _findCategoryByIdRecursive(Category category, String id) {
    for (var child in category.children) {
      if (child.id.toString() == id) {
        return child;
      }
      var subChild = _findCategoryByIdRecursive(child, id);
      if (subChild != null) {
        return subChild;
      }
    }
    return null;
  }

  /// Find parent category for a given child category ID
  static Category? findParentCategory(String childId) {
    for (var category in _categories) {
      for (var child in category.children) {
        if (child.id.toString() == childId) {
          return category; // Parent found
        }
        var parent = _findParentCategoryRecursive(child, childId);
        if (parent != null) {
          return parent;
        }
      }
    }
    return null;
  }

  /// Recursive helper to find parent category
  static Category? _findParentCategoryRecursive(Category category, String childId) {
    for (var child in category.children) {
      if (child.id.toString() == childId) {
        return category;
      }
      var parent = _findParentCategoryRecursive(child, childId);
      if (parent != null) {
        return parent;
      }
    }
    return null;
  }

  /// Get category details for a category
  /// Returns the category's own details or parent's details if category has no details
  static List<CategoryDetails> getCategoryDetails(String categoryId) {
    var category = findCategoryById(categoryId);
    
    if (category != null) {
      if (category.details.isNotEmpty) {
        return category.details;
      } else {
        // Try to get parent category details
        var parentCategory = findParentCategory(categoryId);
        if (parentCategory != null && parentCategory.details.isNotEmpty) {
          return parentCategory.details;
        }
      }
    }
    
    return [];
  }

  /// Get the detail category ID for a given category
  /// Returns the category ID if it has details, or parent category ID if parent has details
  static String getCategoryDetailId(String categoryId) {
    var category = findCategoryById(categoryId);
    
    if (category != null) {
      if (category.details.isNotEmpty) {
        return category.id.toString();
      } else {
        var parentCategory = findParentCategory(categoryId);
        if (parentCategory != null && parentCategory.details.isNotEmpty) {
          return parentCategory.id.toString();
        }
      }
    }
    
    return '';
  }

  /// Get category title with localization support
  static String getCategoryTitle(String categoryId) {
    if (categoryId.isEmpty) {
      return 'select_category'.tr;
    }
    
    if (_categories.isEmpty) {
      return 'select_category'.tr;
    }
    
    Category? selectedCategory = findCategoryById(categoryId);
    return selectedCategory != null ? selectedCategory.getTitle() : 'select_category'.tr;
  }

  /// Find brand ID by name
  static int? findBrandIdByName(String brandName) {
    if (brandName.isEmpty || _brands.isEmpty) {
      return null;
    }

    try {
      return _brands.firstWhere((brand) => brand.name == brandName).id;
    } catch (e) {
      log('Brand not found: $brandName');
      return null;
    }
  }

  /// Clear cached data (useful for testing or when data needs refresh)
  static void clearCache() {
    _categories.clear();
    _brands.clear();
    _categoriesLoaded = false;
  }

  /// Check if categories are loaded
  static bool get areCategoriesLoaded => _categoriesLoaded && _categories.isNotEmpty;

  /// Get cached brands
  static List<Brands> get cachedBrands => _brands;
}
