import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';

import 'package:boutigak/views/widgets/matterport_webview_page.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:ui';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/constants/app_colors.dart';





class ItemBottomSheetContent extends StatefulWidget {
  final Item item;

  const ItemBottomSheetContent({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  State<ItemBottomSheetContent> createState() => _ItemBottomSheetContentState();
}

class _ItemBottomSheetContentState extends State<ItemBottomSheetContent> {
  final PageController pageController = PageController();
  final ConversationController conversationController = Get.put(ConversationController());
  final ItemController itemController = Get.find<ItemController>();
  final AuthController authController = Get.put(AuthController());

  int currentPage = 0;
  bool isFavorited = false;

  @override
  void initState() {
    super.initState();
    itemController.selectItem(widget.item);
    itemController.fetchItemById(widget.item.id!).then((_) {
      final updatedItem = itemController.selectedItem.value;
      if (updatedItem != null) {
        setState(() {
          isFavorited = updatedItem.isLiked;
        });
      }
    });
  }

  void _showPaymentBottomSheet() {
    showDialog(
      context: context,
      builder: (_) => FractionallySizedBox(
        heightFactor: 0.8,
        child: CapturableItemWidget(
          imageUrl: '${widget.item.images.first}',
          title: widget.item.title,
          brand: widget.item.brandName,
          ownerName: widget.item.userName,
          price: widget.item.price,
          itemId: widget.item.id!,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final updatedItem = itemController.selectedItem.value;
    if (updatedItem == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    ImageSlider(
                      pageController: pageController,
                      photos: updatedItem.images
                          .map((image) => '$image')
                          .toList(),
                      currentPage: currentPage,
                      onPageChanged: (int page) => setState(() => currentPage = page),
                      borderRadius: 0,
                    ),
                    _buildShareButton(),
                    _buildCloseButton(),
                    _buildMatterportButton(),
                  ],
                ),
              ),
             Expanded(child: Container()), // reserve for info section overlap
            ],
          ),
          _buildInfoSection(updatedItem),
          _buildBottomSection(updatedItem),
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 60,
      left: 10,
      child: _circleBlurButton(
        icon: FontAwesomeIcons.x,
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildShareButton() {
    return Positioned(
      top: 60,
      right: 10,
      child: _circleBlurButton(
        icon: FontAwesomeIcons.upRightFromSquare,
        onPressed: _showPaymentBottomSheet,
      ),
    );
  }

  Widget _buildMatterportButton() {
    if (widget.item.matterportLink == null || widget.item.matterportLink!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 40,
      right: 10,
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => MatterportView(
                matterportLink: widget.item.matterportLink!,
              ),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(FontAwesomeIcons.cube, color: AppColors.background, size: 24),
              const SizedBox(width: 6),
              Text(
                '3D View',
                style: TextStyle(
                  color: AppColors.background,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection(Item updatedItem) {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.49,
      left: 0,
      right: 0,
      child: Container(
        // decoration: BoxDecoration(
        //   color: Theme.of(context).colorScheme.surface,
        //   borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
        // ),
        child: InfoSection(
          item: updatedItem,
          isFavorited: isFavorited,
          toggleFavorite: () {
            setState(() {
              isFavorited = !isFavorited;
            });
          },
        ),
      ),
    );
  }

  Widget _buildBottomSection(Item updatedItem) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
        ),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 30.0, left: 25, right: 25),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Price (mru)",
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).disabledColor)),
                  Text("${updatedItem.price.toStringAsFixed(0)}",
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface)),
                ],
              ),
              const Spacer(),
              _buildMakeOfferButton(updatedItem),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMakeOfferButton(Item item) {
    return Container(
      width: 200,
      height: 50,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextButton(
        onPressed: () async {
          if (authController.isAuthenticated.value) {
            final success = await conversationController.createDiscussion(item.id!);
            if (success != null) {
              Get.to(() => ConversationPage(
                    itemId: item.id!,
                    item: item.toJson(),
                    discussionId: success['discussion']['id'],
                    interlocutor: item.userName!,
                    phoneNumber: success['discussion']['interlocutor_phone'] ?? null, // Phone number will be fetched from discussion data
                  ));
            }
          } else {
            Get.to(() => LoginPage());
          }
        },
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        ),
        child: Center(
          child: Text(
            "Make an offer",
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _circleBlurButton({required IconData icon, required VoidCallback onPressed}) {
    return ClipOval(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
        child: Container(
          color: AppColors.onBackground.withOpacity(0.3),
          child: IconButton(
            iconSize: 20,
            icon: Icon(icon, color: AppColors.background),
            onPressed: onPressed,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}

class InfoSection extends StatefulWidget {
  final Item item;
  final bool isFavorited;
  final VoidCallback? toggleFavorite;

  const InfoSection({
    Key? key,
    required this.item,
    required this.isFavorited,
    this.toggleFavorite,
  }) : super(key: key);

  @override
  State<InfoSection> createState() => _InfoSectionState();
}

class _InfoSectionState extends State<InfoSection> {
  bool isDescriptionSelected = true;
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    isFavorited = widget.isFavorited;
  }

  @override
  void didUpdateWidget(covariant InfoSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.item != oldWidget.item) {
      isFavorited = widget.item.isLiked;
    }
  }

  Future<void> _toggleFavorite() async {
    bool success = await ItemService.likeUnlikeItem(widget.item.id!, isFavorited);
    if (success) {
      setState(() {
        isFavorited = !isFavorited;
      });
      widget.toggleFavorite?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title + Favorite
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre + Vendeur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.title,
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text("by ",
                            style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),),
                        Text(widget.item.userName ?? '',
                            style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).disabledColor,
                          ),)
                      ],
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _toggleFavorite,
                icon: Icon(
                  isFavorited ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: Colors.redAccent,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
              Text(
                widget.item.createdAt != null
                    ? timeago.format(widget.item.createdAt!)
                    : 'Unknown',
                style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
              )
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.description,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("Condition:", widget.item.condition),
                _buildDetailRow("Category:", widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }


}
