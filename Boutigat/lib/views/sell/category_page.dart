import 'package:boutigak/views/widgets/boutigak_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/session_service.dart';

class CategoryPage extends StatelessWidget {
  final Category? parentCategory;
  final ItemController itemController = Get.find<ItemController>();

  CategoryPage({this.parentCategory});

  @override
  Widget build(BuildContext context) {
    if (parentCategory == null) {
      itemController.fetchCategories();
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text(parentCategory == null ? 'Select Category' : 'Select ${parentCategory!.getTitle()} Subcategory'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: () => _closeAllBottomSheets(context),
        ),
      ),
      body: _buildCategoryList(context),
    );
  }

  Widget _buildCategoryList(BuildContext context) {
    if (parentCategory == null) {
      return Obx(() {
        if (itemController.categories.isEmpty) {
          return Center(
            child: LottieLoopingLoadingWidget(
              animationPath: 'assets/lottie/Boutigaklogo.json', 
            ),
          );
        }
        
        List<Category> categoriesToShow = itemController.categories
            .where((category) => category.parentId == null)
            .toList();
            
        return _buildCategoryListView(context, categoriesToShow);
      });
    } else {
      // For subcategories, we don't need Obx as the parent category's children are already loaded
      return _buildCategoryListView(context, parentCategory!.children);
    }
  }

  Widget _buildCategoryListView(BuildContext context, List<Category> categories) {
    return ListView.separated(
      itemCount: categories.length,
      separatorBuilder: (context, index) => Divider(),
      itemBuilder: (context, index) {
        Category category = categories[index];

        return ListTile(
          title: Text(category.getTitle()),
          trailing: category.children.isNotEmpty 
              ? Icon(Icons.arrow_forward_ios) 
              : Obx(() => Radio<String>(
                  value: category.id.toString(),
                  groupValue: itemController.categoryID.value,
                  onChanged: (value) {
                    itemController.categoryID.value = value!;
                    itemController.updateSelectedCategory(category.id.toString());
                    // fetchBrands() is already called in updateSelectedCategory

                    // Close all bottom sheets by popping until the first one
                    _closeAllBottomSheets(context);
                  },
                )),
          onTap: () {
            if (category.children.isNotEmpty) {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                builder: (context) => FractionallySizedBox(
                  heightFactor: 0.95,
                  child: CategoryPage(parentCategory: category),
                ),
              );
            } else {
              itemController.categoryID.value = category.id.toString();
              itemController.updateSelectedCategory(category.id.toString());
              // fetchBrands() is already called in updateSelectedCategory

              _closeAllBottomSheets(context);
            }
          },
        );
      },
    );
  }

  void _closeAllBottomSheets(BuildContext context) {
    Navigator.of(context).pop();

    if (parentCategory != null) {
      Navigator.of(context).pop();
    }
  }
}

class CategoryDetailsWidget extends StatelessWidget {
  final ItemController controller = Get.find<ItemController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.selectedCategoryDetails.isEmpty) {
        return Center(child: Text("No Details Available"));
      }
      return ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: controller.selectedCategoryDetails.length,
        itemBuilder: (context, index) {
          final detail = controller.selectedCategoryDetails[index];
          return customListTile(detail.labelEn, Icons.arrow_forward, DetailInputPage(detail: detail), detail.labelEn);
        },
      );
    });
  }

  Widget customListTile(String title, IconData icon, Widget page, String currentValue) {
    return InkWell(
      onTap: () => Get.to(page),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 2, horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Text(title, style: TextStyle(fontSize: 14)),
            ),
            Expanded(
              flex: 2,
              child: Text(
                currentValue,
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.right,
              ),
            ),
            Icon(icon, size: 20),
          ],
        ),
      ),
    );
  }
}

class DetailInputPage extends StatelessWidget {
  final CategoryDetails detail;

  DetailInputPage({required this.detail});

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController(text: detail.labelEn);

    return Scaffold(
      appBar: AppBar(
        title: Text('Input Detail'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            TextField(
              controller: controller,
              decoration: InputDecoration(labelText: 'Detail'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Save the detail or perform any action needed
                // For example, you can update the detail in your ItemController
                Navigator.pop(context);
              },
              child: Text('Save'),
            ),
          ],
        ),
      ),
    );
  }
}
