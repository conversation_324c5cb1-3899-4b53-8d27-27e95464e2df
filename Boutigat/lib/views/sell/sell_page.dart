import 'dart:developer';

import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/controllers/status_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'sell_widgets.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/constants/app_colors.dart';

class SellPage extends StatefulWidget {
  final int? itemId;
  
  const SellPage({Key? key, this.itemId}) : super(key: key);
  
  @override
  _SellPageState createState() => _SellPageState();
}

class _SellPageState extends State<SellPage> {
  final ItemController itemController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>(); // Add this
  
  @override
  void initState() {
    super.initState();
    _loadItemData();
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();
    showValidationErrors.value = false;

    super.dispose();
  }
  
  Future<void> _loadItemData() async {
    if (widget.itemId != null) {
      await itemController.loadItemForEdit(widget.itemId!);
    }
  }
  final RxString uploadStatus = "Preparing upload...".obs;
  final int totalSteps = 4;
  final PhotoActionsController photoController = Get.put(
      PhotoActionsController(Get.find<ItemController>()),
      permanent: true);
  final RxBool isItemUploaded = false.obs;
  final RxDouble uploadProgress = 0.0.obs;
  final RxBool showValidationErrors = false.obs;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'sell_an_item'.tr,
          style: TextStyle(
              color: Theme.of(context).colorScheme.surface,
              fontSize: AppTextSizes.heading),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey, // Wrap everything in a Form
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: <Widget>[
              // Main content - widgets handle their own shimmer loading
              Column(
                children: <Widget>[
                  SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        PhotoActionsWidget(),
                        Obx(() {
                          // Show error if no image and validation errors should be shown
                          if (photoController.photos.isEmpty && showValidationErrors.value) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                "Veuillez ajouter au moins une photo.",
                                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                              ),
                            );
                          }
                          return SizedBox.shrink();
                        }),
                        SizedBox(height: 20),
                        ItemDetailsEntryMainLanguage(formKey: _formKey, showValidationErrors: showValidationErrors),
                        SizedBox(height: 20),
                        ItemDetailsEntryArabic(formKey: _formKey, showValidationErrors: showValidationErrors),
                        SizedBox(height: 20),
                        ItemDetailsFormWidget(formKey: _formKey, showValidationErrors: showValidationErrors),
                          Obx(() {
                            if (itemController.selectedCategoryDetails.isEmpty) {
                              return const Center(child: Text(""));
                            }
                            return CategoryDetailsWidget(showValidationErrors: showValidationErrors);
                          }),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Padding(
                              padding: EdgeInsets.only(bottom: 30),
                              child: Obx(() {
                                return Column(
                                  children: [
                                    if (uploadProgress.value > 0)
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                        child: Container()
                                      ),
                                    SizedBox(height: 10),
                                    CustomButton(
                                      text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                                      onPressed: () async {
                                        // Enable validation error display
                                        showValidationErrors.value = true;

                                        // Force rebuild of all form fields to show validation
                                        setState(() {});

                                        // Wait a frame for the UI to update
                                        await Future.delayed(Duration(milliseconds: 100));

                                        // Check form validation
                                        bool isFormValid = _formKey.currentState!.validate();

                                        // Check additional validations
                                        bool hasPhotos = photoController.photos.isNotEmpty;
                                        bool hasCategory = itemController.categoryID.value.isNotEmpty;
                                        bool hasBrand = itemController.brand.value.isNotEmpty;

                                        if (!isFormValid || !hasPhotos || !hasCategory || !hasBrand) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text("Veuillez remplir tous les champs obligatoires.")),
                                          );
                                          return;
                                        }

                                        // Clear validation errors since form is valid
                                        showValidationErrors.value = false;

                                        log('in update');
                                        uploadProgress.value = 0.0;
                                        uploadStatus.value = "Préparation de l'envoi...";

                                        showUploadProgressDialog(context, uploadStatus, uploadProgress);

                                        if (widget.itemId != null) {
                                          await itemController.updateItem(widget.itemId!);
                                          itemController.clearItemData();
                                          photoController.clearPhotoActionData();
                                          itemController.clearFields();

                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text("item_updated_successfully".tr)),
                                          );

                                          uploadProgress.value = 0.0;
                                          uploadStatus.value = "";

                                           Navigator.of(context).pop(); // Dismiss dialog
                                          Get.back();
                                          Get.find<StatusController>().fetchMyItems();
                                         
                                        } else {
                                          try {
                                            uploadProgress.value = 0.25;
                                            uploadStatus.value = "Validation des données...";
                                            await Future.delayed(Duration(milliseconds: 500));

                                            uploadProgress.value = 0.5;
                                            uploadStatus.value = "Téléchargement des images...";
                                            
                                            uploadProgress.value = 0.75;
                                            uploadStatus.value = "Création de l'article...";
                                            
                                            int? uploadedItemId = await itemController.postItem((progress) {
                                              uploadProgress.value = 0.75 + (progress * 0.25);
                                            });

                                            uploadProgress.value = 1.0;
                                            uploadStatus.value = "Envoi terminé !";

                                            if (itemController.isItemUploaded.value) {
                                              Navigator.of(context).pop(); // Dismiss dialog
                                              Get.to(
                                                PaymentWidget(
                                                  amount: itemController.categoryPri.value,
                                                  initialAmount: itemController.categoryPri.value,
                                                  itemId: uploadedItemId!,
                                                )
                                              );

                                              ScaffoldMessenger.of(context).showSnackBar(
                                                SnackBar(content: Text("item_uploaded_successfully".tr)),
                                              );
                                              
                                              itemController.clearItemData();
                                              photoController.clearPhotoActionData();
                                              itemController.clearFields();
                                              
                                              await Future.delayed(Duration(seconds: 1));
                                              uploadProgress.value = 0.0;
                                              uploadStatus.value = "";
                                            }
                                          } catch (e) {
                                            uploadStatus.value = "Échec de l'envoi. Veuillez réessayer.";
                                            Navigator.of(context).pop(); // Dismiss dialog
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(content: Text("Erreur lors de l'envoi : ${e.toString()}")),
                                            );
                                          }
                                        }
                                      },
                                    ),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void showPaymentBottomSheet(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7, 
            child: PaymentForm(onConfirm: () {
              Navigator.pop(context); 
              showConfirmationBottomSheet(context);
            }),
          );
        },
      );
    });
  }

  void showConfirmationBottomSheet(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7, // 70% of the screen height
            child: ConfirmationSheet(),
          );
        },
      );
    });
  }

  void showUploadProgressDialog(BuildContext context, RxString status, RxDouble progress) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Obx(() => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              UploadProgressWidget(
                progress: progress.value,
                status: status.value,
                isComplete: progress.value >= 1.0,
              ),
            ],
          )),
        ),
      ),
    );
  }
}

class PaymentForm extends StatefulWidget {
  final VoidCallback onConfirm;

  PaymentForm({required this.onConfirm});

  @override
  State<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends State<PaymentForm> {
  final PaymentController paymentController = Get.put(PaymentController());

  InputDecoration inputDecoration(String labelText) {
    return InputDecoration(
      labelText: labelText,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(15),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    paymentController.fetchPaymentProviders();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16.0,
        right: 16.0,
        top: 24.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 120,
            height: 100,
            child: Image.asset(
              "assets/images/bankily.jpg",
              fit: BoxFit.fitHeight,
            ),
          ),
          SizedBox(height: 8.0),
          Obx(() {
            if (paymentController.isLoading.isTrue) {
              return const CircularProgressIndicator();
            } else if (paymentController.isError.isTrue) {
              return const Text(
                  'Erreur lors du chargement des méthodes de paiement');
            } else {
              // Ensure the selectedProvider exists in the list of paymentProviders
              String? selectedProviderValue =
                  paymentController.selectedProvider.value.isEmpty
                      ? null
                      : paymentController.selectedProvider.value;

              // Ensure the list is not empty
              if (paymentController.paymentProviders.isEmpty) {
                selectedProviderValue = null;
              }

              return DropdownButtonFormField<String>(
                value: selectedProviderValue, // Use the corrected value
                onChanged: (newValue) {
                  // Update the selected provider
                  paymentController.selectedProvider.value = newValue ?? '';
                },
                items: paymentController.paymentProviders
                    .map<DropdownMenuItem<String>>((PaymentProvider provider) {
                  return DropdownMenuItem<String>(
                    value: provider.id.toString(), // Ensure uniqueness
                    child: Text(provider.name),
                  );
                }).toList(),
                decoration: inputDecoration('Méthode de paiement'),
              );
            }
          }),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Service:",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    "Abonnement annuel ",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Total à payer (mru)",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                  Text(
                    "1,200 MRU",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Code B-PAY :',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              SizedBox(
                width: 10,
              ),
              Text(
                '003271',
                style: TextStyle(fontWeight: FontWeight.normal, fontSize: 18),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          TextField(
            decoration: InputDecoration(
              labelText: 'Entrer le numéro BANKILY',
              border: OutlineInputBorder(),
            ),
          ),
          SizedBox(height: 16.0),
          TextField(
            decoration: InputDecoration(
              labelText: 'Entrer le passcode de paiement',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
          ),
          SizedBox(height: 24.0),
          ElevatedButton(
            onPressed: widget.onConfirm,
            child: Text('Confirmer'),
          ),
        ],
      ),
    );
  }
}

class ConfirmationSheet extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16.0,
        right: 16.0,
        top: 24.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 50),
          SizedBox(height: 16.0),
          Text(
            'Paiement Succès',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
          ),
          SizedBox(height: 16.0),
          Text('Transaction ID : 012209271029102241'),
          Text('Montant de paiement : 1,200 MRU'),
          Text('Date de paiement : 29-09-2022 10:29'),
          Text('Service : abonnement annuel Auto'),
          SizedBox(height: 24.0),
          ElevatedButton(
            onPressed: () {
              // Handle PDF download here
              Navigator.pop(context);
            },
            child: Text('Telecharger le reçu PDF'),
          ),
        ],
      ),
    );
  }
}
