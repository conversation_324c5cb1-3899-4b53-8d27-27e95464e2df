import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/brands_service.dart';

class BrandPage extends StatelessWidget {
  final ItemController itemController = Get.find<ItemController>();
  final TextEditingController newBrandController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // Check if category is selected before allowing brand selection
    if (itemController.categoryID.value.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Select Brand'),
          backgroundColor: Theme.of(context).colorScheme.surface,
          iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.warning, size: 64, color: Colors.orange),
              SizedBox(height: 16),
              Text(
                'Veuillez d\'abord sélectionner une catégorie',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Retour'),
              ),
            ],
          ),
        ),
      );
    }

    // Brands should already be loaded when category was selected
    // Only fetch if brands are empty
    if (itemController.brands.isEmpty) {
      itemController.fetchBrands();
    }
    final brands = itemController.brands;

    return Scaffold(
      appBar: AppBar(
        title: Text('Select Brand'),
      backgroundColor: Theme.of(context).colorScheme.surface,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
      ),
      body: Column(
        children: [
          SizedBox(height: 10),
          Expanded(
            child: ListView.builder(
              itemCount: brands.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    RadioListTile<String>(
                      title: Text(brands[index].name),
                      value: brands[index].name,
                      groupValue: itemController.brand.value,
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          itemController.brand.value = newValue;
                          Navigator.pop(context);
                        }
                      },
                      fillColor: MaterialStateProperty.all(AppColors.primary),
                      controlAffinity: ListTileControlAffinity.trailing,
                      dense: true,
                    ),
                    Divider(),
                  ],
                );
              },
            ),
          ),
         
          SizedBox(height: 20),
        ],
      ),
    );
  }
}
