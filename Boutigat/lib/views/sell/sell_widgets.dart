import 'dart:async';
import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'condition_page.dart';
import 'brand_page.dart';
import 'category_page.dart';
import 'dart:io';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:shimmer/shimmer.dart';

// Shimmer widgets for loading states
class ShimmerTextInput extends StatelessWidget {
  final double height;
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerTextInput({
    Key? key,
    this.height = 56.0,
    this.margin = const EdgeInsets.symmetric(vertical: 4),
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Container(
        padding: padding,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label shimmer
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(height: 12),
              // Input field shimmer
              Container(
                width: double.infinity,
                height: height,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShimmerListTile extends StatelessWidget {
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerListTile({
    Key? key,
    this.margin = const EdgeInsets.symmetric(vertical: 4),
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Container(
        padding: padding,
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Row(
            children: [
              // Icon shimmer
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8),
              // Label shimmer
              Expanded(
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(width: 16),
              // Value shimmer
              Container(
                width: 100,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(width: 8),
              // Arrow shimmer
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ShimmerTextArea extends StatelessWidget {
  final EdgeInsets margin;
  final EdgeInsets padding;

  const ShimmerTextArea({
    Key? key,
    this.margin = const EdgeInsets.only(bottom: 20),
    this.padding = const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      margin: margin,
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Container(
              padding: padding,
              child: Column(
                children: [
                  // Title field shimmer
                  Container(
                    width: double.infinity,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: 16),
                  // Divider shimmer
                  Container(
                    width: double.infinity,
                    height: 1,
                    color: Colors.grey[300],
                  ),
                  SizedBox(height: 16),
                  // Description field shimmer (multiple lines)
                  ...List.generate(4, (index) => Padding(
                    padding: EdgeInsets.only(bottom: 8),
                    child: Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
class PhotoActionsWidget extends StatelessWidget {
  final PhotoActionsController controller = Get.find<PhotoActionsController>();

  PhotoActionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: <Widget>[
          Padding(
            padding: EdgeInsets.only(bottom: sidePadding, top: sidePadding),
            child: Text(
              "add_up_to_10_photos".tr,
              style: TextStyle(
                fontSize: AppTextSizes.bodyText,
                fontWeight: AppFontWeights.regular,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Obx(() {



            print('photos added: ${controller.photos.value}');
            if (controller.photos.isEmpty) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  buildActionButton("take_photo".tr, FontAwesomeIcons.camera,
                      controller.takePhoto, controller.photoButtonColor,context),
                  buildActionButton("upload_photo".tr,
                      FontAwesomeIcons.arrowUpFromBracket, controller.uploadPhoto, controller.uploadButtonColor,context),
                ],
              );
            } else {
              return Column(
                children: [
                  SizedBox(
                    height: 100,
                    child: ReorderableListView(
                      scrollDirection: Axis.horizontal,
                      proxyDecorator: (child, index, animation) => Material(
                        type: MaterialType.transparency,
                        child: child,
                      ),
                      children: <Widget>[
                        ...controller.photos.map((photoPath) => Container(
                              key: ValueKey(photoPath),
                              margin: const EdgeInsets.symmetric(horizontal: 8.0),
                              child: buildPhotoView(context, photoPath),
                            )).toList(),
                        buildAddMoreButton(key: const ValueKey('add_more_button')),
                      ],
                      onReorder: (oldIndex, newIndex) {
                        if (oldIndex < controller.photos.length && newIndex <= controller.photos.length) {
                          if (newIndex > controller.photos.length) {
                            newIndex = controller.photos.length;
                          }
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          controller.reorderPhotos(oldIndex, newIndex);
                        }
                      },
                    ),
                  ),
                ],
              );
            }
          }),
          SizedBox(height: 5),
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              "hold_and_drag".tr,
              style: TextStyle(
                fontSize: AppTextSizes.bodySmall,
                fontWeight: AppFontWeights.regular,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 5),
        ],
      ),
    );
  }



  Widget buildActionButton(String text, IconData icon, VoidCallback onPressed, Rx<Color> buttonColor,context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 30.0),
      child: SizedBox(
        width: 172.5,
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.background,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
              side: BorderSide(color: Theme.of(context).disabledColor, width: 1),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(() => Icon(icon, color: buttonColor.value)),
              SizedBox(width: 10),
              Obx(() => Text(text, style: TextStyle(color: buttonColor.value,fontSize: AppTextSizes.bodySmall))),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildPhotoView(BuildContext context, String photoPath) {
    // Check if the path is a network URL or local file path
    final bool isNetworkImage = photoPath.startsWith('http://') || photoPath.startsWith('https://');



 
    print('photoPath: $photoPath');
    print('isNetworkImage: $isNetworkImage');
    final ImageProvider imageProvider = isNetworkImage 
        ? NetworkImage(photoPath)
        : FileImage(File(photoPath)) as ImageProvider;
    
    return GestureDetector(
      onTap: () => openImageSlider(context, photoPath),
      child: Stack(
        alignment: Alignment.topRight,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
            width: 140,
            height: 100,
            child: isNetworkImage 
                ? Image.network(
                     photoPath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
                      );
                    },
                  )
                : null,
          ),
        ],
      ),
    );
  }

  Widget buildAddMoreButton({required Key key}) {
    return IconButton(
      key: key,
      icon: Icon(Icons.add_circle, color: AppColors.primary, size: 35),
      onPressed: () {
  if (controller.photos.length >= 10) {
    Get.snackbar("Limit reached", "You can only add up to 10 photos.");
    return;
  }

  Get.bottomSheet(
  Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Theme.of(Get.context!).colorScheme.background,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            buildActionButton(
              "take_photo".tr,
             FontAwesomeIcons.camera,

              () {
                Get.back();
                controller.takePhoto();
              },
              controller.photoButtonColor,
              Get.context!,
            ),
             buildActionButton(
          "upload_photo".tr,
          FontAwesomeIcons.arrowUpFromBracket,
          () {
            Get.back();
            controller.uploadPhoto();
          },
          controller.uploadButtonColor,
          Get.context!,
        ),
          ],
        ),
        const SizedBox(height: 12),
       
      ],
    ),
  ),
);

}

    );
  }

  void openImageSlider(BuildContext context, String initialPhoto) {
    final initialPage = controller.photos.indexOf(initialPhoto);
    int currentPage = controller.photos.indexOf(initialPhoto);

    showDialog(
      context: context,
      builder: (context) {
        double screenWidth = MediaQuery.of(context).size.width;
        double screenHeight = MediaQuery.of(context).size.height;
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.zero,
              child: Container(
                width: screenWidth,
                height: screenHeight * 0.6,
                child: Stack(
                  children: [
                    PageView.builder(
                      controller: PageController(initialPage: initialPage),
                      itemCount: controller.photos.length,
                      onPageChanged: (int page) => setState(() => currentPage = page),
                      itemBuilder: (context, index) {
                        final photoPath = controller.photos[index];
                        final bool isNetworkImage = photoPath.startsWith('http://') || photoPath.startsWith('https://');
                        
                        return isNetworkImage
                            ? Image.network(
                                photoPath,
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[300],
                                    child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
                                  );
                                },
                              )
                            : Image.file(
                                File(photoPath),
                                fit: BoxFit.contain,
                              );
                      },
                    ),
                    // Pagination indicators
                    Positioned(
                      bottom: 10,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          controller.photos.length,
                          (index) => Container(
                            margin: EdgeInsets.symmetric(horizontal: 4),
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: currentPage == index ? Colors.white : Colors.white.withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Delete button
                    Positioned(
                      right: 0,
                      top: 0,
                      child: IconButton(
                        icon: Icon(FontAwesomeIcons.deleteLeft, color: Color.fromARGB(255, 255, 92, 71)),
                        onPressed: () {
                          controller.removePhoto(controller.photos[currentPage]);
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class ItemDetailsFormWidget extends StatefulWidget {
  final GlobalKey<FormState>? formKey;
  final RxBool? showValidationErrors;
  const ItemDetailsFormWidget({Key? key, this.formKey, this.showValidationErrors}) : super(key: key);

  @override
  _ItemDetailsFormWidgetState createState() => _ItemDetailsFormWidgetState();
}

class _ItemDetailsFormWidgetState extends State<ItemDetailsFormWidget> {
  final ItemController itemController = Get.find<ItemController>();
  late TextEditingController priceController;
  bool _priceTouched = false;
  late Worker _loadingWorker; // Add this to track the worker

  @override
  void initState() {
    super.initState();
    priceController = TextEditingController(
      text: itemController.price.value == 0.0 ? '' : itemController.price.value.toString()
    );

    // Listen for when loading finishes, then update priceController
    _loadingWorker = ever(itemController.isLoadingItemForEdit, (isLoading) {
      if (!isLoading && mounted) { // Check if widget is still mounted
        priceController.text = itemController.price.value == 0.0 ? '' : itemController.price.value.toString();
        if (mounted) { // Double check before calling setState
          setState(() {}); // Safe to call here, not in build
        }
      }
    });
  }

  String? _validatePrice(String? value) {
    if (!_priceTouched && widget.showValidationErrors?.value != true) {
      return null; // Don't show error if not touched and not force validating
    }
    return value == null || value.isEmpty ? 'Ce champ est requis' : null;
  }

  @override
  void dispose() {
    _loadingWorker.dispose(); // Dispose the worker first
    priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Obx(() {
          bool isEditMode = itemController.itemId.value != null;
          if (isEditMode && itemController.isLoadingItemForEdit.value) {
            return Column(
              children: [
                ShimmerListTile(),
                ShimmerListTile(),
                ShimmerListTile(),
                SizedBox(height: 16),
                ShimmerTextInput(height: 56),
                SizedBox(height: 8),
              ],
            );
          }

          // Do NOT call setState here!
          // Just use priceController as normal

          // Check if this is a store item by looking at the selected item
          bool isStoreItem = false;
          if (isEditMode && itemController.selectedItem.value != null) {
            log('store id  ..... ${itemController.selectedItem.value!.storeId}');
            isStoreItem = itemController.selectedItem.value!.storeId != null;

          }
          
          // Allow category editing for store items, disable for regular items in edit mode
          bool shouldShowCategory = !isEditMode || isStoreItem;
          
          return shouldShowCategory
            ? Container(
                color: Theme.of(context).colorScheme.surface,
                margin: EdgeInsets.symmetric(vertical: 4),
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Catégorie',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 12),
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            builder: (context) => FractionallySizedBox(
                              heightFactor: 0.95,
                              child: CategoryPage(parentCategory: null),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            border: Border.all(
                              color: (widget.showValidationErrors?.value == true && itemController.categoryID.value.isEmpty)
                                ? Colors.red
                                : Colors.grey.shade300,
                              width: (widget.showValidationErrors?.value == true && itemController.categoryID.value.isEmpty) ? 2 : 1
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Obx(() => Text(
                                  itemController.selectedCategoryTitle.isEmpty ? 'Sélectionner une catégorie *' : itemController.selectedCategoryTitle,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: itemController.selectedCategoryTitle.isEmpty ? Colors.grey.shade400 : Colors.black87,
                                    fontWeight: itemController.selectedCategoryTitle.isEmpty ? FontWeight.normal : FontWeight.w500,
                                  ),
                                )),
                              ),
                              Icon(Icons.arrow_drop_down, size: 24, color: Colors.grey.shade600),
                            ],
                          ),
                        )),
                      ),
                    ),
                  ],
                ),
              )
            : Container();
        }),
        
        // Inline Brand Selection
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Marque',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 12),
              Obx(() {
                bool isCategorySelected = itemController.categoryID.value.isNotEmpty;
                bool isLoadingBrands = itemController.isLoadingBrands.value;
                bool hasValidationError = widget.showValidationErrors?.value == true && itemController.brand.value.isEmpty;

                String displayText;
                if (!isCategorySelected) {
                  displayText = 'Sélectionnez d\'abord une catégorie';
                } else if (isLoadingBrands) {
                  displayText = 'Chargement des marques...';
                } else if (itemController.brand.value.isEmpty) {
                  displayText = 'Sélectionner une marque *';
                } else {
                  displayText = itemController.brand.value;
                }

                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: (isCategorySelected && !isLoadingBrands) ? () => Get.to(BrandPage()) : null,
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                      decoration: BoxDecoration(
                        color: (isCategorySelected && !isLoadingBrands) ? Colors.grey.shade50 : Colors.grey.shade100,
                        border: Border.all(
                          color: hasValidationError
                            ? Colors.red
                            : ((isCategorySelected && !isLoadingBrands) ? Colors.grey.shade300 : Colors.grey.shade400),
                          width: hasValidationError ? 2 : 1
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              displayText,
                              style: TextStyle(
                                fontSize: 16,
                                color: (!isCategorySelected || isLoadingBrands)
                                  ? Colors.grey.shade500
                                  : (itemController.brand.value.isEmpty ? Colors.grey.shade400 : Colors.black87),
                                fontWeight: itemController.brand.value.isEmpty ? FontWeight.normal : FontWeight.w500,
                              ),
                            ),
                          ),
                          if (isLoadingBrands)
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                              ),
                            )
                          else
                            Icon(
                              Icons.arrow_drop_down,
                              size: 24,
                              color: (isCategorySelected && !isLoadingBrands) ? Colors.grey.shade600 : Colors.grey.shade400
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),

        // Inline Condition Selection
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'État',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 12),
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => Get.to(ConditionPage()),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      border: Border.all(color: Colors.grey.shade300, width: 1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Obx(() => Text(
                            itemController.condition.value.isEmpty ? "Sélectionner l'état *" : itemController.condition.value,
                            style: TextStyle(
                              fontSize: 16,
                              color: itemController.condition.value.isEmpty ? Colors.grey.shade400 : Colors.black87,
                              fontWeight: itemController.condition.value.isEmpty ? FontWeight.normal : FontWeight.w500,
                            ),
                          )),
                        ),
                        Icon(Icons.arrow_drop_down, size: 24, color: Colors.grey.shade600),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        Container(
          color: Theme.of(context).colorScheme.surface,
          margin: EdgeInsets.symmetric(vertical: 4),
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Prix',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 12),
              TextFormField(
                controller: priceController,
                validator: _validatePrice,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                textInputAction: TextInputAction.done,
                style: TextStyle(fontSize: 16),
                onTap: () {
                  setState(() {
                    _priceTouched = true;
                  });
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                decoration: InputDecoration(
                  hintText: 'Prix *',
                  hintStyle: TextStyle(
                    color: Colors.grey.shade400,
                    fontSize: 16,
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppColors.primary, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  suffixText: 'UM',
                  suffixStyle: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                onChanged: (value) {
                  double? newPrice = double.tryParse(value.replaceAll(',', '.'));
                  if (newPrice != null) {
                    itemController.price.value = newPrice;
                  } else if (value.isEmpty) {
                    itemController.price.value = 0.0;
                  }
                },
                onFieldSubmitted: (_) => FocusScope.of(context).nextFocus(), // Move to next field
              ),
            ],
          ),
        ),
        SizedBox(height: 8),
      ],
    );
  }


}


// For main language and arabic widgets, use TextFormField with validator:
class ItemDetailsEntryMainLanguage extends StatefulWidget {
  final GlobalKey<FormState>? formKey;
  final RxBool? showValidationErrors;
  const ItemDetailsEntryMainLanguage({this.formKey, this.showValidationErrors, super.key});

  @override
  _ItemDetailsEntryMainLanguageState createState() => _ItemDetailsEntryMainLanguageState();
}

class _ItemDetailsEntryMainLanguageState extends State<ItemDetailsEntryMainLanguage> {
  late final GlobalKey<FormFieldState> _titleFieldKey;
  late final GlobalKey<FormFieldState> _descriptionFieldKey;
  StreamSubscription? _validationListener;

  // Track if fields have been touched
  bool _titleTouched = false;
  bool _descriptionTouched = false;

  @override
  void initState() {
    super.initState();
    _titleFieldKey = GlobalKey<FormFieldState>();
    _descriptionFieldKey = GlobalKey<FormFieldState>();

    // Listen to validation changes
    _validationListener = widget.showValidationErrors?.listen((shouldShow) {
      // Trigger validation on existing fields when upload button is clicked
      if (shouldShow && mounted) {
        _titleFieldKey.currentState?.validate();
        _descriptionFieldKey.currentState?.validate();
      }
    });
  }

  @override
  void dispose() {
    _validationListener?.cancel();
    super.dispose();
  }

  String? _validateTitle(String? value) {
    if (!_titleTouched && widget.showValidationErrors?.value != true) {
      return null; // Don't show error if not touched and not force validating
    }
    return value == null || value.isEmpty || value.length < 3 ? 'Ce champ est requis' : null;
  }

  String? _validateDescription(String? value) {
    if (!_descriptionTouched && widget.showValidationErrors?.value != true) {
      return null; // Don't show error if not touched and not force validating
    }
    return value == null || value.isEmpty ? 'Ce champ est requis' : null;
  }

  @override
  Widget build(BuildContext context) {
    final itemController = Get.find<ItemController>();
    return Obx(() {
      // Show shimmer if loading item for edit
      if (itemController.isLoadingItemForEdit.value) {
        return ShimmerTextArea();
      }
      return Container(
        color: Theme.of(context).colorScheme.surface,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Titre de l'annonce",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12),
                TextFormField(
                  key: _titleFieldKey,
                  controller: itemController.titleController,
                  validator: _validateTitle,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  style: TextStyle(fontSize: 16),
                  onTap: () {
                    setState(() {
                      _titleTouched = true;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: "Titre de l'annonce *",
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  ),
                ),
              ],
            ),

            SizedBox(height: 24),

            // Description Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Description",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12),
                TextFormField(
                  key: _descriptionFieldKey,
                  controller: itemController.descriptionController,
                  validator: _validateDescription,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  maxLines: 3,
                  style: TextStyle(fontSize: 16),
                  textInputAction: TextInputAction.done,
                  onTap: () {
                    setState(() {
                      _descriptionTouched = true;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: "Description *",
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    alignLabelWithHint: true,
                  ),
                  onFieldSubmitted: (_) => FocusScope.of(context).unfocus(),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}

class ItemDetailsEntryArabic extends StatefulWidget {
  final GlobalKey<FormState>? formKey;
  final RxBool? showValidationErrors;
  const ItemDetailsEntryArabic({this.formKey, this.showValidationErrors, super.key});

  @override
  _ItemDetailsEntryArabicState createState() => _ItemDetailsEntryArabicState();
}

class _ItemDetailsEntryArabicState extends State<ItemDetailsEntryArabic> {
  late final GlobalKey<FormFieldState> _titleArFieldKey;
  late final GlobalKey<FormFieldState> _descriptionArFieldKey;
  StreamSubscription? _validationListener;

  // Track if fields have been touched
  bool _titleArTouched = false;
  bool _descriptionArTouched = false;

  @override
  void initState() {
    super.initState();
    _titleArFieldKey = GlobalKey<FormFieldState>();
    _descriptionArFieldKey = GlobalKey<FormFieldState>();

    // Listen to validation changes
    _validationListener = widget.showValidationErrors?.listen((shouldShow) {
      // Trigger validation on existing fields when upload button is clicked
      if (shouldShow && mounted) {
        _titleArFieldKey.currentState?.validate();
        _descriptionArFieldKey.currentState?.validate();
      }
    });
  }

  @override
  void dispose() {
    _validationListener?.cancel();
    super.dispose();
  }

  String? _validateTitleAr(String? value) {
    if (!_titleArTouched && widget.showValidationErrors?.value != true) {
      return null; // Don't show error if not touched and not force validating
    }
    return value == null || value.isEmpty ? 'Ce champ est requis' : null;
  }

  String? _validateDescriptionAr(String? value) {
    if (!_descriptionArTouched && widget.showValidationErrors?.value != true) {
      return null; // Don't show error if not touched and not force validating
    }
    return value == null || value.isEmpty ? 'Ce champ est requis' : null;
  }

  @override
  Widget build(BuildContext context) {
    final itemController = Get.find<ItemController>();

    return Obx(() {
      // Show shimmer if loading item for edit
      if (itemController.isLoadingItemForEdit.value) {
        return ShimmerTextArea();
      }

      return Container(
        color: Theme.of(context).colorScheme.surface,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Arabic Title Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "عنوان الإعلان",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12),
                TextFormField(
                  key: _titleArFieldKey,
                  controller: itemController.titleArController,
                  validator: _validateTitleAr,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  style: TextStyle(fontSize: 16),
                  textDirection: TextDirection.rtl,
                  onTap: () {
                    setState(() {
                      _titleArTouched = true;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: "عنوان الإعلان *",
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  ),
                ),
              ],
            ),

            SizedBox(height: 24),

            // Arabic Description Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "الوصف",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12),
                TextFormField(
                  key: _descriptionArFieldKey,
                  controller: itemController.descriptionArController,
                  validator: _validateDescriptionAr,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  maxLines: 3,
                  style: TextStyle(fontSize: 16),
                  textDirection: TextDirection.rtl,
                  textInputAction: TextInputAction.done,
                  onTap: () {
                    setState(() {
                      _descriptionArTouched = true;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: "الوصف *",
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    alignLabelWithHint: true,
                  ),
                  onFieldSubmitted: (_) => FocusScope.of(context).unfocus(),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}



class CategoryDetailsWidget extends StatefulWidget {
  final GlobalKey<FormState>? formKey;
  final RxBool? showValidationErrors;
  const CategoryDetailsWidget({super.key, this.formKey, this.showValidationErrors});

  @override
  _CategoryDetailsWidgetState createState() => _CategoryDetailsWidgetState();
}

class _CategoryDetailsWidgetState extends State<CategoryDetailsWidget> {
  final ItemController controller = Get.find<ItemController>();
  Map<String, TextEditingController> detailControllers = {};

  @override
  void dispose() {
    for (var controller in detailControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  TextEditingController _getControllerForDetail(CategoryDetails detail) {
    final key = detail.id.toString();
    if (!detailControllers.containsKey(key)) {
      detailControllers[key] = TextEditingController(
        text: detail.value?.isNotEmpty == true ? detail.value : ''
      );
    }
    return detailControllers[key]!;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Show shimmer if loading categories or brands
      if (controller.isLoadingCategories.value || controller.isLoadingBrands.value) {
        return Column(
          children: List.generate(3, (index) => ShimmerTextInput()),
        );
      }

      // If category is selected but no details are available, show empty state
      if (controller.categoryID.value.isNotEmpty && controller.selectedCategoryDetails.isEmpty) {
        return Container(); // Empty container when no details are needed
      }

      // If no category is selected, don't show anything
      if (controller.categoryID.value.isEmpty) {
        return Container();
      }

      return ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: controller.selectedCategoryDetails.length,
        itemBuilder: (context, index) {
          final detail = controller.selectedCategoryDetails[index];

          return Container(
            color: Theme.of(context).colorScheme.surface,
            margin: EdgeInsets.symmetric(vertical: 4),
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.getlabel(),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12),
                TextFormField(
                  controller: _getControllerForDetail(detail),
                  validator: (value) => value == null || value.isEmpty ? 'Ce champ est requis' : null,
                  autovalidateMode: AutovalidateMode.disabled, // Will be controlled by form validation
                  style: TextStyle(fontSize: 16),
                  decoration: InputDecoration(
                    hintText: '${detail.getlabel()} *',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  ),
                  onChanged: (value) {
                    detail.value = value;
                  },
                ),
              ],
            ),
          );
        },
      );
    });
  }

}
