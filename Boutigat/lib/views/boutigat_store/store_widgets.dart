import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:get/get.dart';

import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/text_row_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'dart:ui';

import 'package:share_plus/share_plus.dart';

class ClosedStatusWidget extends StatelessWidget {
  final String openingTime;
  final double? iconSize;
  ClosedStatusWidget(
      {Key? key, required this.openingTime, required this.iconSize})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 5),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FontAwesomeIcons.clock,
              color: Theme.of(context).colorScheme.surface, size: iconSize),
          SizedBox(width: 8),
          Text(
            'Schedule for $openingTime',
            style: TextStyle(
                color: Theme.of(context).colorScheme.surface,
                fontSize: 10,
                fontWeight: AppFontWeights.bold),
          ),
        ],
      ),
    );
  }
}

class BoutiquesListWidget extends StatefulWidget {
  BoutiquesListWidget({Key? key}) : super(key: key);

  @override
  State<BoutiquesListWidget> createState() => _BoutiquesListWidgetState();
}

class _BoutiquesListWidgetState extends State<BoutiquesListWidget> {
  // init state

  @override
  Widget build(BuildContext context) {
    final StoreController storeController = Get.find<StoreController>();

    // Appeler fetchPromotedStores pour charger les boutiques si la liste est vide
    if (storeController.recommendedStores.isEmpty) {
      storeController.fetchRecomandedStores(refresh: true);
    }

    return Obx(() {
      // Si les boutiques sont en train d'être chargées, afficher un loader
      if (storeController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      // Si la liste des boutiques est vide, afficher un message
      if (storeController.recommendedStores.isEmpty) {
        return const Center(child: Text("Aucune boutique disponible."));
      }

      // Créer la liste de widgets pour chaque boutique
      List<Widget> boutiqueWidgets =
          storeController.recommendedStores.map((boutique) {
        return BoutiqueWidget(
          boutique: boutique,
          fontSize: 18,
          iconSize: 12,
        );
      }).toList();

      // Retourner une colonne contenant tous les widgets des boutiques
      return Column(
        children: boutiqueWidgets,
      );
    });
  }
}

// class BoutiqueWidget extends StatelessWidget {
//   final StoreController boutique;
//   final double? fontSize;
//   final double? iconSize;
//   BoutiqueWidget(
//       {Key? key,
//       required this.boutique,
//       required this.fontSize,
//       required this.iconSize})
//       : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     // Map<String, dynamic> promoDetails = boutique.getPromotionDetails();
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.stretch,
//       children: [
//         Stack(
//           children: [
//             GestureDetector(
//               onTap: () {
//               //  Get.to(() => BoutiqueDetailsPage(boutique: boutique));
//               },
//               child: Padding(
//                 padding:
//                     const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
//                 child: ClipRRect(
//                   borderRadius: BorderRadius.all(Radius.circular(15)),
//                   child: Image.asset(
//                     boutique.boutiquePictures.length > 1
//                         ? boutique.boutiquePictures[1]
//                         : boutique.boutiquePictures[0],
//                     fit: BoxFit.fill,
//                     height: 160,
//                     width: double.infinity,
//                   ),
//                 ),
//               ),
//             ),
//             // if (promoDetails['percent'] > 0)
//             //   Positioned(
//             //     top: 20,
//             //     left: 4,
//             //     child: Container(
//             //       padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
//             //       decoration: BoxDecoration(
//             //         color: Theme.of(context).colorScheme.surface,
//             //         borderRadius: BorderRadius.circular(5),
//             //       ),
//             //       child: Row(
//             //         mainAxisSize: MainAxisSize.min,
//             //         children: [
//             //           Container(
//             //             padding: EdgeInsets.all(3),
//             //             decoration: BoxDecoration(
//             //               color: Colors.yellow,
//             //               shape: BoxShape.circle,
//             //             ),
//             //             child: Icon(Icons.local_offer,
//             //                 color: Theme.of(context).colorScheme.onSurface, size: 10),
//             //           ),
//             //           SizedBox(width: 4),
//             //           Text(
//             //             '${promoDetails['percent']}% ${promoDetails['type']}',
//             //             style: TextStyle(
//             //               fontSize: 10,
//             //               fontWeight: FontWeight.bold,
//             //             ),
//             //           ),
//             //         ],
//             //       ),
//             //     ),
//             //   ),
//           ],
//         ),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Row(
//                 children: [
//                   Image.asset(
//                     'assets/images/sac-de-courses.png',
//                     width: 15,
//                     height: 15,
//                   ),
//                   Text(
//                     boutique.name.value,
//                     style: TextStyle(
//                       fontSize: fontSize,
//                       fontWeight: AppFontWeights.extraBold,
//                     ),
//                   ),
//                 ],
//               ),
//               Container(
//                 padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
//                 decoration: BoxDecoration(
//                   color: Colors.grey[200],
//                   borderRadius: BorderRadius.circular(5),
//                 ),
//                 child: Text(
//                   boutique.type.value,
//                   style: TextStyle(
//                     fontSize: 10,
//                     fontWeight: FontWeight.bold,
//                     color: AppColors.disabled,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//         Padding(
//           padding: EdgeInsets.symmetric(
//             horizontal: 16,
//             vertical: 4,
//           ),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.start,
//             children: [
//               if (!boutique.isOpenNow())
//                 ClosedStatusWidget(
//                   openingTime: boutique.openingHours.value,
//                   iconSize: iconSize,
//                 ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }
class BoutiqueWidget extends StatelessWidget {
  final Store
      boutique; // Change this to accept Store instead of StoreController
  final double? fontSize;
  final double? iconSize;

  BoutiqueWidget({
    Key? key,
    required this.boutique, // Now it takes a Store object
    required this.fontSize,
    required this.iconSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Assuming that your Store model has fields like name, type, boutiquePictures, etc.
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Stack(
          children: [
            GestureDetector(
              onTap: () {
                // Navigation to details page can use boutique data here
                // Get.to(() => BoutiqueDetailsPage(boutique: boutique));
              },
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
                child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(15)),
                    child: Image(
                      image: boutique.images.isNotEmpty
                          ? NetworkImage('${boutique.images.first}')
                          : AssetImage(
                              'assets/images/placeholder_logo.png'), // Use AssetImage for local assets
                      fit: BoxFit.cover,
                      height: 160,
                      width: double.infinity,
                    )),
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset(
                    'assets/images/sac-de-courses.png',
                    width: 15,
                    height: 15,
                  ),
                  SizedBox(width: 4),
                  Text(
                    boutique.name, // Assuming boutique.name is a String
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  boutique.typeId.toString(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 4,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (!boutique
                  .isOpenNow()) // Assuming isOpenNow() is a method in the Store model
                ClosedStatusWidget(
                  openingTime: boutique
                      .openingTime, // Assuming boutique.openingHours is a String
                  iconSize: iconSize,
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class StoreitemsListViewWidget extends StatelessWidget {
  final Category? category;
  final List<Item> items;
  final String storeImage;
  final String storeId;
  StoreitemsListViewWidget({
    Key? key,
    this.category,
    required this.items,
    required this.storeImage,
    required this.storeId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final StoreController storeController = Get.find<StoreController>();

    // print('item id: ${items[0].id}');
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Obx(() {
      // Show loader when items are being fetched
      if (storeController.isLoadingItems.value) {
        return _buildShimmerEffect(context, screenWidth, sidePadding);
      }

      if (items.isEmpty) {
        return Center(
          child: Text("No items available"),
        );
      }

      return _buildItemsList(context, screenWidth, sidePadding);
    });
  }

  Widget _buildShimmerEffect(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                height: 20,
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 517,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildShimmerItem(context, screenWidth);
              },
              childCount: 6, // Show 6 shimmer items
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerItem(BuildContext context, double screenWidth) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer image placeholder
          Container(
            width: screenWidth * 0.4388,
            height: screenWidth * 0.5485,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: _shimmerAnimation(),
          ),
          SizedBox(height: 8),
          // Shimmer text placeholders
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 13,
                width: screenWidth * 0.3,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _shimmerAnimation(),
              ),
              SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    height: 13,
                    width: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _shimmerAnimation(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _shimmerAnimation() {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1.0),
      duration: Duration(milliseconds: 1000),
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey[300]!.withOpacity(0.3),
                Colors.grey[100]!.withOpacity(value),
                Colors.grey[300]!.withOpacity(0.3),
              ],
              stops: [0.0, 0.5, 1.0],
            ),
          ),
        );
      },
      onEnd: () {
        // Animation will automatically repeat due to the way TweenAnimationBuilder works
      },
    );
  }

  Widget _buildItemsList(
      BuildContext context, double screenWidth, double sidePadding) {
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                "${items.length} Products",
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.normal),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(
              left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 517,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return _buildItemWidget(
                    context, items[index], screenWidth, storeId);
              },
              childCount: items.length,
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to safely convert promotion percentage to string
  String _getPromotionPercentageAsString(dynamic promotionPercentage) {
    if (promotionPercentage == null) return "0";

    if (promotionPercentage is String) {
      double? parsed = double.tryParse(promotionPercentage);
      return parsed?.toStringAsFixed(0) ?? "0";
    } else if (promotionPercentage is num) {
      return promotionPercentage.toStringAsFixed(0);
    }

    return "0";
  }

  // Helper method to safely convert price to string
  String _getPriceAsString(dynamic price) {
    if (price == null) return "0";

    if (price is String) {
      double? parsed = double.tryParse(price);
      return parsed?.toStringAsFixed(0) ?? "0";
    } else if (price is num) {
      return price.toStringAsFixed(0);
    }

    return "0";
  }

  Widget _buildItemWidget(
      BuildContext context, Item item, double screenWidth, String storeId) {
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              _showModalBottomSheet(context, item, storeImage, storeId);
            },
            child: Stack(
              children: [
                CachedImageWidget(
                  imageUrl:
                      item.images.isNotEmpty ? '${item.images.first}' : '',
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                ),
                if (item.hasPromotion ?? false)
                  Positioned(
                    top: 15,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(5),
                          bottomRight: Radius.circular(5),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.discount,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: 15),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            "${item.promotionPercentage}% Discount",
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 10,
                              fontWeight: AppFontWeights.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.getTitle(),
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: AppColors.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${_getPriceAsString(item.price)} ',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: AppFontWeights.extraBold,
                    ),
                  ),
                  Text(
                    'mru',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: AppFontWeights.extraBold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }

  void _addToOrder(Item item, int quantity, BuildContext context) {
    // Initialize OrderController with lazyPut to ensure it's only created when needed
    final OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));

    final ItemController itemController = Get.put(ItemController());
    // Find if item exists in the order
    var existingOrderItem = orderController.items.firstWhereOrNull(
      (orderItem) => orderItem.itemId.value == item.id,
    );

    // If item exists, increment quantity
    if (existingOrderItem != null) {
      existingOrderItem.incrementQuantity();
    } else {
      // Add new item to order
      var newItem = OrderItemController(
        initialItemId: item.id!,
        initialQuantity: quantity,
      );
      orderController.addItem(newItem);
    }

    Get.back(); // closes the bottom sheet

    // Recalculate the total
    // orderController.calculateTotal();

    // Navigator.pop(context);
    // orderController.update();
  }

  String _generateDeepLink(String storeId, String itemId) {
    return 'https://app.boutigak.com/stores/$storeId/items/$itemId';
  }

  void _showModalBottomSheet(
      BuildContext context, Item item, String storeImage, String storeId) {
    PageController pageController = PageController();
    int currentPage = 0;
    bool isFavorited = false;
    int quantity = 1;
    OrderController orderController =
        Get.put(OrderController(initialOrderId: 1));

    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Container(
              height: MediaQuery.of(context).size.height,
              child: Stack(
                children: [
                  Column(
                    children: [
                      Expanded(
                        child: Stack(
                          children: [
                            ImageSlider(
                              pageController: pageController,
                              photos:
                                  item.images.map((image) => '$image').toList(),
                              currentPage: currentPage,
                              onPageChanged: (int page) =>
                                  setState(() => currentPage = page),
                              borderRadius: 0,
                            ),
                            Positioned(
                              top: 60,
                              right: 10,
                              child: ClipOval(
                                child: BackdropFilter(
                                  filter:
                                      ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                  child: Container(
                                    color:
                                        AppColors.onBackground.withOpacity(0.2),
                                    child: IconButton(
                                      iconSize: 20,
                                      icon: const Icon(
                                          FontAwesomeIcons.upRightFromSquare,
                                          color: AppColors.background),
                                      onPressed: () async {
                                        String itemId = item.id.toString();

                                        String deepLink =
                                            _generateDeepLink(storeId, itemId);
                                        await Share.share(
                                            'Découvrez ce produit : $deepLink');
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 60,
                              left: 10,
                              child: ClipOval(
                                child: BackdropFilter(
                                  filter:
                                      ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                  child: Container(
                                    color:
                                        AppColors.onBackground.withOpacity(0.3),
                                    child: IconButton(
                                      iconSize: 20,
                                      icon: const Icon(
                                          FontAwesomeIcons.chevronDown,
                                          color: AppColors.background),
                                      onPressed: () => Navigator.pop(context),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(child: Container()),
                    ],
                  ),
                  Expanded(
                    child: Container(
                      
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(15)),
                      ),
                      child: InfoSectionWidget(
                        boutiquePhotoUrl: storeImage,
                        item: item,
                        //    orderController: orderController,
                        isFavorited: isFavorited,
                        toggleFavorite: () async => {
                          await ItemService.likeUnlikeItem(
                            item.id!,
                            !isFavorited,
                          ),
                          setState(() => isFavorited = !isFavorited)
                        },
                        dominantColor: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(15)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(
                            bottom: 30.0, left: 25, right: 25),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Quantity',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: AppFontWeights.medium),
                                ),
                                Container(
                                  padding: EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          if (quantity > 1) {
                                            setState(() => quantity--);
                                          }
                                        },
                                        borderRadius: BorderRadius.circular(10),
                                        child: Padding(
                                          padding: EdgeInsets.all(4),
                                          child: Icon(Icons.remove, size: 16),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 13),
                                        child: Text(
                                          quantity.toString(),
                                          style: TextStyle(fontSize: 16),
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          setState(() => quantity++);
                                        },
                                        borderRadius: BorderRadius.circular(10),
                                        child: Padding(
                                          padding: EdgeInsets.all(4),
                                          child: Icon(Icons.add, size: 16),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20),
                            Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Price (mru)",
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: Theme.of(context).disabledColor,
                                      ),
                                    ),
                                    Text(
                                      "${_getPriceAsString(item.price * quantity)} ",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                                Spacer(),
                                Row(
                                  children: [
                                    // Ask about product button
                                    Container(
                                      width: 95,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .surface,
                                        border: Border.all(
                                            color: AppColors.primary),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: TextButton(
                                        onPressed: () async {
                                          log('item_to_json ${item.toJson()}');

                                          // fetch store info
                                          Store? store =
                                              await StoreService.getStoreById(
                                                  int.parse(storeId));

                                          log('store_name ${store}');

                                          final authController =
                                              Get.find<AuthController>();
                                          final conversationController =
                                              Get.put(ConversationController());

                                          if (authController
                                              .isAuthenticated.value) {
                                            dynamic success =
                                                await conversationController
                                                    .createStoreDiscussion(
                                                        item.id!,
                                                        item.storeId!);

                                            final User? user =
                                                authController.user;

                                            log('connected user ${success}');

                                            if (success != null) {
                                              Get.to(() => ConversationPage(
                                                    itemId: item.id!,
                                                    item: item.toJson(),
                                                    discussionId: int.parse(
                                                        success['discussion']
                                                                ['id']
                                                            .toString()),
                                                    interlocutor: store!.name,
                                                    isStoreDiscussion: true,
                                                    user: user?.toJson(),
                                                    phoneNumber: success[
                                                                'discussion'][
                                                            'interlocutor_phone'] ??
                                                        null,
                                                  ));
                                            }
                                          } else {
                                            Get.to(() => LoginPage());
                                          }
                                        },
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.zero,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Container(
                                          alignment: Alignment.center,
                                          child: const Text(
                                            "Ask about",
                                            style: TextStyle(
                                              color: AppColors.primary,
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                    // Add to cart button
                                    Container(
                                      width: 95,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.2),
                                            spreadRadius: 2,
                                            blurRadius: 5,
                                            offset: const Offset(0, 3),
                                          ),
                                        ],
                                      ),
                                      child: TextButton(
                                        onPressed: () async {
                                          _addToOrder(item, quantity, context);
                                        },
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.zero,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Container(
                                          alignment: Alignment.center,
                                          child: const Text(
                                            "Add to Cart",
                                            style: TextStyle(
                                              color: AppColors.primary,
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
