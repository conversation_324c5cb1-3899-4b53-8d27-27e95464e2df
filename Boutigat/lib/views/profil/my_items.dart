import 'dart:developer';
import 'dart:ui';
import 'package:boutigak/controllers/status_controller.dart';
import 'package:boutigak/data/models/status.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/sell/sell_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';

import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '/constants/app_colors.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:boutigak/controllers/badge_controller.dart';

class MyItemsPage extends StatefulWidget {
  @override
  _MyItemsPageState createState() => _MyItemsPageState();
}

class _MyItemsPageState extends State<MyItemsPage> {
  final ItemController itemController = Get.put(ItemController());
  final BadgeController badgeController = Get.find<BadgeController>();

  @override
  void initState() {
    super.initState();

    // Reset the badge when the page is opened
    badgeController.resetBadge('items');
  }

  void _navigateToHome() async {
    Get.offAll(
      () => ZoomDrawerWrapper(shouldOpenDrawer: true),
      transition: Transition.noTransition,
    );
  }

  @override
  Widget build(BuildContext context) {
    Get.put(StatusController());
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            _navigateToHome();
          },
        ),
        title: Text(
          'Post',
          style: TextStyle(color: Theme.of(context).colorScheme.surface),
        ),
        backgroundColor: AppColors.primary,
      ),
      body: Container(
        color: Colors.white, // Set the background color here
        child: MyItemsListWidget(),
      ),
    );
  }
}

class MyItemsListWidget extends StatefulWidget {
  @override
  _MyItemsListWidgetState createState() => _MyItemsListWidgetState();
}

class _MyItemsListWidgetState extends State<MyItemsListWidget> {
  final StatusController statusController = Get.find<StatusController>();
  bool _isLoading = false;

  // Function to refresh the liked items
  Future<void> _refreshMyItems() async {
    setState(() {
      _isLoading = true;
    });

    statusController.fetchMyItems();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    // Initial data load
    _refreshMyItems();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: LiquidPullToRefresh(
        onRefresh: _refreshMyItems, // Refresh function
        color: AppColors.primary, // Customize pull-to-refresh colors
        backgroundColor: Theme.of(context).colorScheme.surface,
        height: 200.0,
        child: Obx(() {
          var myItemsToShow = statusController.myItems;

          if (_isLoading || myItemsToShow.isEmpty) {
            // Show shimmer loading effect when liked items are empty or still loading
            return ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(vertical: 16.0),
              itemCount: 10, // Show 10 shimmer placeholders
              itemBuilder: (context, index) {
                return Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15.0, vertical: 10.0),
                    child: Row(
                      children: [
                        // Placeholder for image
                        Container(
                          width: 50.0,
                          height: 50.0,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        SizedBox(width: 10.0),
                        // Placeholder for title and price
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 200,
                                height: 16.0,
                                color: Colors.grey[300],
                              ),
                              SizedBox(height: 5.0),
                              Container(
                                width: 100.0,
                                height: 14.0,
                                color: Colors.grey[300],
                              ),
                            ],
                          ),
                        ),
                        // Placeholder for favorite icon
                        Container(
                          width: 30.0,
                          height: 30.0,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          } else {
            // Show actual liked items
            return ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(vertical: 8.0),
              itemCount: myItemsToShow.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    MyItemWidget(
                      status: myItemsToShow[index],
                    ),
                  ],
                );
              },
            );
          }
        }),
      ),
    );
  }
}

class MyItemWidget extends StatefulWidget {
  final Status status;

  MyItemWidget({
    Key? key,
    required this.status,
  }) : super(key: key);

  @override
  _MyItemWidgetState createState() => _MyItemWidgetState();
}

class _MyItemWidgetState extends State<MyItemWidget> {
  late bool isFavorited;
  late StatusController statusController;
  late ItemController itemController;

  @override
  void initState() {
    super.initState();

    // Initialisation des contrôleurs ici
    statusController = Get.find<StatusController>();
    itemController = Get.find<ItemController>();

    // Set the item in the controller to be used reactively
    statusController.selectedItem(widget.status);
  }

  void _showModalBottomSheet(BuildContext context, Status status) {
    PageController pageController = PageController();
    int currentPage = 0;

    // Affiche le loader pendant la récupération des données
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => Container(
        color: Colors.black.withOpacity(0.4),
        child: const Center(
          child: CupertinoActivityIndicator(radius: 15),
        ),
      ),
    );

    itemController.fetchItemById(int.parse(status.id)).then((_) async {
      Navigator.pop(context); // Ferme le loader
      await Future.delayed(
          const Duration(milliseconds: 100)); // Attente anti-conflit

      showModalBottomSheet(
        backgroundColor: Theme.of(context).colorScheme.surface,
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Obx(() {
                final updatedItem = itemController.selectedItem.value;

                if (updatedItem == null) {
                  return const Center(child: CircularProgressIndicator());
                }

                return Container(
                  height: MediaQuery.of(context).size.height,
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          Expanded(
                            child: Stack(
                              children: [
                                ImageSlider(
                                  pageController: pageController,
                                  photos: updatedItem.images
                                      .map((image) => '$image')
                                      .toList(),
                                  currentPage: currentPage,
                                  onPageChanged: (int page) =>
                                      setState(() => currentPage = page),
                                  borderRadius: 0,
                                ),
                                Positioned(
                                  top: 60,
                                  right: 10,
                                  child: ClipOval(
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                          sigmaX: 3, sigmaY: 3),
                                      child: Container(
                                        color: AppColors.onBackground
                                            .withOpacity(0.2),
                                        child: IconButton(
                                          iconSize: 20,
                                          icon: Icon(
                                              FontAwesomeIcons
                                                  .upRightFromSquare,
                                              color: AppColors.background),
                                          onPressed: () =>
                                              print('Share Icon Tapped!'),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  top: 60,
                                  left: 10,
                                  child: ClipOval(
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                          sigmaX: 3, sigmaY: 3),
                                      child: Container(
                                        color: AppColors.onBackground
                                            .withOpacity(0.3),
                                        child: IconButton(
                                          iconSize: 20,
                                          icon: Icon(
                                              FontAwesomeIcons.chevronDown,
                                              color: AppColors.background),
                                          onPressed: () =>
                                              Navigator.pop(context),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Expanded(child: Container()),
                        ],
                      ),
                      Positioned(
                        top: MediaQuery.of(context).size.height * 0.49,
                        left: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(15)),
                          ),
                          child: Column(
                            children: [
                              InfoSectionMyItems(
                                item: updatedItem,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(15)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                bottom: 30.0, left: 25, right: 25),
                            child: Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Price (mru)",
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      "${updatedItem.price.toStringAsFixed(0)} ",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                Container(
                                  width: 120,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () {
                                      Get.to(() => SellPage(
                                          itemId: int.parse(status.id)));
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: const Center(
                                      child: Text(
                                        "Edit",
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  width: 120,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: TextButton(
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            title:
                                                const Text("Confirm Deletion"),
                                            content: const Text(
                                                "Are you sure you want to delete this item?"),
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text("Cancel"),
                                              ),
                                              ElevatedButton(
                                                onPressed: () {
                                                  itemController.deleteItem(
                                                      int.parse(status.id));
                                                  Navigator.of(context)
                                                      .pop(); // Close confirm
                                                  Navigator.of(context)
                                                      .pop(); // Close bottom sheet
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      AppColors.error,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            15),
                                                  ),
                                                ),
                                                child: const Text(
                                                  "Delete",
                                                  style: TextStyle(
                                                      color: AppColors.surface),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    child: const Center(
                                      child: Text(
                                        "Delete",
                                        style: TextStyle(
                                          color: AppColors.surface,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              });
            },
          );
        },
      );
    });
  }

  Widget _buildStatusWidget(Status status) {
    Widget buildStatusBox(
        {required IconData icon, required String text, required Color color}) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 5),
            Text(
              text,
              style: TextStyle(
                  fontSize: 12, fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
      );
    }

    if (status.status == "APPROVED") {
      if (status.isPromoted) {
        return buildStatusBox(
            icon: Icons.star,
            text: "Approved and Promoted",
            color: Colors.green);
      }
      return buildStatusBox(
          icon: Icons.check_circle, text: "Approved", color: Colors.green);
    } else if (status.status == "REJECTED") {
      return buildStatusBox(
          icon: Icons.cancel, text: "Rejected", color: Colors.red);
    } else if (status.isPaid != null && status.isPaid == false) {
      return buildStatusBox(
          icon: Icons.attach_money,
          text: "En attente de paiement",
          color: Colors.orange);
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: const [
            SizedBox(
              width: 14,
              height: 14,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            SizedBox(width: 5),
            Text(
              "En cours d'examen",
              style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () async {
          final itemId = int.parse(widget.status.id);

          // Mettre à jour manuellement l'élément sélectionné (optionnel)
          itemController.selectedItem.value = null;

          // Appel asynchrone pour récupérer les données à jour
          await itemController.fetchItemById(itemId);

          // Affiche le bottom sheet une fois les données prêtes
          _showModalBottomSheet(context, widget.status);
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0.4, 0.4),
              ),
            ],
          ),
          child: Row(
            children: [
              CachedImageWidget(
                imageUrl: '${widget.status.images.first}',
                width: 70,
                height: 80,
                fit: BoxFit.cover,
                borderRadius: BorderRadius.circular(8.0),
              ),
              const SizedBox(width: 8.0),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Première ligne : titre + Help
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.status.title,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        HelpButton(),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Deuxième ligne : statut et action
                    Row(
                      children: [
                        _buildStatusWidget(widget.status),
                        const Spacer(),
                        if (widget.status.status == "REJECTED")
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              widget.status.rejectionReason ?? 'Rejected',
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                        else if (widget.status.isPaid != null &&
                            !widget.status.isPaid &&
                            widget.status.status != "REJECTED" &&
                            widget.status.status != "APPROVED")
                          GestureDetector(
                            onTap: () {
                              Get.to(() => PaymentWidget(
                                    amount: double.parse(
                                        widget.status.categoryPrice ?? '0'),
                                    itemId: int.parse(widget.status.id),
                                    isOrderPayment: false,
                                    isPaid: false,
                                    initialAmount: double.parse(
                                        widget.status.categoryPrice ?? '0'),
                                  ));
                            },
                            child: Container(
                              width: 50,
                              height: 30,
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: Text(
                                  'pay',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}

class InfoSectionMyItems extends StatefulWidget {
  final Item item;

  const InfoSectionMyItems({Key? key, required this.item}) : super(key: key);

  @override
  _InfoSectionMyItemsState createState() => _InfoSectionMyItemsState();
}

class _InfoSectionMyItemsState extends State<InfoSectionMyItems> {
  bool isDescriptionSelected = true;

  @override
  void didUpdateWidget(covariant InfoSectionMyItems oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.item != oldWidget.item) {
      // Si tu veux faire quelque chose quand l'item change
      setState(() {}); // Optionnel selon ton usage
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title + User info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre + Vendeur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.getTitle(),
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text("by ",
                            style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),),
                        Text(widget.item.userName ?? '',
                            style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).disabledColor,
                          ),)
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Date
          Row(
            children: [
              const Spacer(),
              Text(
                widget.item.createdAt != null
                    ? timeago.format(widget.item.createdAt!)
                    : 'Unknown',
                style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
              )
            ],
          ),

          const SizedBox(height: 16),

          // Description Section
          Text(
            "Description",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.getDescription(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
            ),
          ),

          const SizedBox(height: 24),

          // Details Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Details",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow("Condition:", widget.item.condition),
                _buildDetailRow("Category:", widget.item.categoryName ?? "Unknown"),
                _buildDetailRow("Brand:", widget.item.brandName ?? "Unknown"),
                // Add category details if available
                if (widget.item.categoryItemDetails.isNotEmpty)
                  ...widget.item.categoryItemDetails.map<Widget>((detail) {
                    // Pick label based on locale, fallback to English
                    String label = detail.labelEn;
                    if (Get.locale?.languageCode == 'ar') {
                      label = detail.labelAr;
                    } else if (Get.locale?.languageCode == 'fr') {
                      label = detail.labelFr;
                    }
                    String value = detail.value.toString();
                    return _buildDetailRow("$label:", value);
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HelpButton extends StatelessWidget {
  const HelpButton({super.key});

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text('Besoin d’aide ?'),
        content: const Text(
          'Contactez-nous si vous avez un problème, ou si vous souhaitez promouvoir votre annonce pour qu’elle apparaisse en premier.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              const phoneNumber = '0022236666688';
              final url = Uri.parse("https://wa.me/$phoneNumber");

              if (await canLaunchUrl(url)) {
                await launchUrl(url, mode: LaunchMode.externalApplication);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text("Impossible d'ouvrir WhatsApp.")),
                );
              }
            },
            icon: Icon(Icons.phone),
            label: const Text("Contactez-nous"),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showHelpDialog(context),
      child: Container(
        width: 18,
        height: 18,
        decoration: BoxDecoration(
          color: Colors.grey[700], // gris foncé
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: Text(
            '?',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
